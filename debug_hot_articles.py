#!/usr/bin/env python3
"""
调试热门文章接口的工具脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, '/usr/local/data/steam_Aggregation_backend')

from app.db.redis import sorted_set_get_range, redis_master_client


async def check_redis_hot_pool():
    """检查Redis热门推荐池的状态"""
    print("🔍 检查Redis热门推荐池状态...")
    
    # 检查文章热门池
    article_key = "rec_pool:hot:article"
    try:
        # 获取总数量
        total_count = await redis_master_client.zcard(article_key)
        print(f"📊 {article_key} 总数量: {total_count}")
        
        if total_count > 0:
            # 获取前10条数据
            top_items = await sorted_set_get_range(
                article_key, 0, 9, with_scores=True, desc=True
            )
            print(f"🔥 前10条热门文章:")
            for i, (item, score) in enumerate(top_items, 1):
                print(f"  {i}. ID: {item}, Score: {score}")
        else:
            print("⚠️ 热门文章池为空！")
            
    except Exception as e:
        print(f"❌ 检查文章热门池失败: {e}")
    
    # 检查视频热门池
    video_key = "rec_pool:hot:video"
    try:
        total_count = await redis_master_client.zcard(video_key)
        print(f"📊 {video_key} 总数量: {total_count}")
        
        if total_count > 0:
            top_items = await sorted_set_get_range(
                video_key, 0, 4, with_scores=True, desc=True
            )
            print(f"🎬 前5条热门视频:")
            for i, (item, score) in enumerate(top_items, 1):
                print(f"  {i}. ID: {item}, Score: {score}")
        else:
            print("⚠️ 热门视频池为空！")
            
    except Exception as e:
        print(f"❌ 检查视频热门池失败: {e}")


async def add_test_data():
    """添加测试数据到热门推荐池"""
    print("\n🧪 添加测试数据...")
    
    article_key = "rec_pool:hot:article"
    test_data = {
        "1": 100.0,
        "2": 95.0,
        "3": 90.0,
        "4": 85.0,
        "5": 80.0,
    }
    
    try:
        await redis_master_client.zadd(article_key, test_data)
        print(f"✅ 已添加测试数据到 {article_key}")
        
        # 验证添加结果
        count = await redis_master_client.zcard(article_key)
        print(f"📊 当前 {article_key} 数量: {count}")
        
    except Exception as e:
        print(f"❌ 添加测试数据失败: {e}")


async def main():
    """主函数"""
    print("🚀 开始调试热门文章接口...\n")
    
    # 检查当前状态
    await check_redis_hot_pool()
    
    # 询问是否添加测试数据
    print("\n" + "="*50)
    response = input("是否添加测试数据？(y/N): ").strip().lower()
    
    if response in ['y', 'yes']:
        await add_test_data()
        print("\n" + "="*50)
        print("📋 添加测试数据后的状态:")
        await check_redis_hot_pool()
    
    print("\n🎯 调试建议:")
    print("1. 如果Redis池为空，请运行热门推荐更新任务")
    print("2. 检查 app/tasks/recommendation_tasks.py 中的定时任务")
    print("3. 手动运行: python -c 'from app.tasks.recommendation_tasks import update_hot_recommendations; update_hot_recommendations()'")
    print("4. 或者使用上面的测试数据进行调试")


if __name__ == "__main__":
    asyncio.run(main())
