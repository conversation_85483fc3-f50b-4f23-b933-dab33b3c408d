"""
推荐服务
"""

from sqlalchemy.ext.asyncio import AsyncSession

from app.crud import user_behavior as crud_user_behavior
from app.db.redis import (
    hash_get,
    sorted_set_add,
    sorted_set_get_range,
    sorted_set_get_score,
)
from app.schemas.recommendation import (
    PaginatedRecommendationResponse,
    RecommendationItem,
)


class RecommendationService:
    """推荐服务类"""

    async def _get_user_group(self, user_id: int) -> int | None:
        """从Redis获取用户推荐群组ID"""
        group_id_raw = await hash_get("user_to_group", str(user_id))
        return int(group_id_raw) if group_id_raw else None

    async def get_recommendations(
        self,
        db: AsyncSession,
        user_id: int,
        content_type: str,
        page: int,
        page_size: int,
    ) -> PaginatedRecommendationResponse:
        """获取推荐列表"""
        # 1. 获取用户所属的推荐群组
        user_group = await self._get_user_group(user_id)

        # 2. 从群组推荐池中获取内容
        recommendations = await self.get_recs_from_group_pool(
            user_id, user_group, content_type, page, page_size
        )

        # 3. 如果群组推荐不足，用热门内容补充
        if len(recommendations) < page_size:
            hot_recs = await self.get_recs_from_hot_pool(content_type, page, page_size)
            # 合并并去重
            existing_ids = {rec.content_id for rec in recommendations}
            for rec in hot_recs:
                if rec.content_id not in existing_ids:
                    recommendations.append(rec)
                    if len(recommendations) >= page_size:
                        break

        # 4. 如果仍然不足，用最新内容补充
        if len(recommendations) < page_size:
            latest_recs = await self.get_recs_from_latest_pool(content_type, page, page_size)
            existing_ids = {rec.content_id for rec in recommendations}
            for rec in latest_recs:
                if rec.content_id not in existing_ids:
                    recommendations.append(rec)
                    if len(recommendations) >= page_size:
                        break

        return PaginatedRecommendationResponse(
            items=recommendations, page=page, page_size=page_size
        )

    async def get_recs_from_group_pool(
        self,
        user_id: int,
        group_id: int,
        content_type: str,
        page: int,
        page_size: int,
    ) -> list[RecommendationItem]:
        """从按类型细化的群组推荐池中获取数据"""
        group_id_raw = await hash_get("user_to_group", str(user_id))
        if not group_id_raw:
            return []
        group_id = int(group_id_raw)

        rec_pool_key = f"rec_pool:group:{group_id}:{content_type}"
        start = (page - 1) * page_size
        end = start + page_size - 1
        recs = await sorted_set_get_range(rec_pool_key, start, end, with_scores=True, desc=True)
        return [RecommendationItem(content_id=int(item), score=score) for item, score in recs]

    async def get_recs_from_hot_pool(
        self, content_type: str, page: int, page_size: int
    ) -> list[RecommendationItem]:
        """从热门推荐池中获取数据"""
        try:
            rec_pool_key = f"rec_pool:hot:{content_type}"
            start = (page - 1) * page_size
            end = start + page_size - 1
            recs = await sorted_set_get_range(rec_pool_key, start, end, with_scores=True, desc=True)
            return [RecommendationItem(content_id=int(item), score=score) for item, score in recs]
        except Exception:
            return []

    async def get_recs_from_latest_pool(
        self, content_type: str, page: int, page_size: int
    ) -> list[RecommendationItem]:
        """从最新推荐池中获取数据"""
        try:
            rec_pool_key = f"rec_pool:latest:{content_type}"
            start = (page - 1) * page_size
            end = start + page_size - 1
            recs = await sorted_set_get_range(rec_pool_key, start, end, with_scores=True, desc=True)
            return [RecommendationItem(content_id=int(item), score=score) for item, score in recs]
        except Exception:
            return []

    async def add_impression(
        self, db: AsyncSession, user_id: int, recommendations: list[RecommendationItem]
    ):
        """记录曝光（用户看到了推荐）"""
        behaviors = [
            {
                "user_id": user_id,
                "content_id": rec.content_id,
                "content_type": rec.content_type,
                "behavior_type": "impression",
            }
            for rec in recommendations
        ]
        await crud_user_behavior.create_multi(db, obj_in=behaviors)

    async def add_user_feedback(
        self,
        db: AsyncSession,
        user_id: int,
        content_id: int,
        content_type: str,
        feedback_type: str,
    ):
        """记录用户反馈（如点击、点赞、收藏等）"""
        behavior = {
            "user_id": user_id,
            "content_id": content_id,
            "content_type": content_type,
            "behavior_type": feedback_type,
        }
        await crud_user_behavior.create(db, obj_in=behavior)

        # 根据反馈更新推荐分数
        await self.update_recommendation_score(user_id, content_id, content_type, feedback_type)

    async def update_recommendation_score(
        self, user_id: int, content_id: int, content_type: str, feedback_type: str
    ):
        """根据用户反馈更新推荐分数"""
        # 定义不同反馈类型的分数权重
        score_weights = {
            "click": 1.0,
            "like": 2.0,
            "favorite": 3.0,
            "comment": 4.0,
            "share": 5.0,
        }
        score_increment = score_weights.get(feedback_type, 0)

        if score_increment > 0:
            # 更新热门推荐池
            hot_pool_key = f"rec_pool:hot:{content_type}"
            await sorted_set_add(hot_pool_key, {str(content_id): score_increment})

            # 更新用户所属群组的推荐池
            user_group = await self._get_user_group(user_id)
            if user_group:
                group_pool_key = f"rec_pool:group:{user_group}:{content_type}"
                await sorted_set_add(group_pool_key, {str(content_id): score_increment})

    async def get_content_score(self, content_type: str, content_id: int) -> float | None:
        """获取内容的推荐分数"""
        try:
            item_key = f"{content_type}:{content_id}"
            score = await sorted_set_get_score("rec_pool:hot:all", item_key)
            return score
        except Exception:
            return None

    async def update_hot_pool(self, content_type: str, content_id: int, score: float):
        """更新热门推荐池"""
        try:
            rec_pool_key = f"rec_pool:hot:{content_type}"
            await sorted_set_add(rec_pool_key, {str(content_id): score})
        except Exception:
            pass

    async def get_hot_content(
        self,
        content_type: str,
        limit: int,
        offset: int = 0,
    ) -> PaginatedRecommendationResponse:
        """获取热门内容（支持偏移量分页）

        Args:
            content_type: 内容类型 ("article" 或 "video")
            limit: 每页大小
            offset: 偏移量

        Returns:
            PaginatedRecommendationResponse: 分页的推荐响应
        """
        try:
            rec_pool_key = f"rec_pool:hot:{content_type}"
            start = offset
            end = start + limit - 1

            # 获取比需要多一个的数据，用于判断是否有下一页
            extra_items = await sorted_set_get_range(
                rec_pool_key, start, end + 1, with_scores=True, desc=True
            )

            # 判断是否有下一页
            has_next = len(extra_items) > limit

            # 取实际需要的数据
            items_data = extra_items[:limit]

            # 转换为 RecommendationItem 对象
            items = []
            for item, score in items_data:
                try:
                    items.append(
                        RecommendationItem(
                            content_type=content_type,
                            content_id=int(item),
                            score=float(score),
                            reason="hot_content",
                        )
                    )
                except (ValueError, TypeError):
                    continue

            return PaginatedRecommendationResponse(
                items=items,
                has_next=has_next,
            )

        except Exception:
            # 降级返回空结果
            return PaginatedRecommendationResponse(
                items=[],
                has_next=False,
            )

    async def get_paginated_content_recommendations(
        self,
        user_id: int,
        content_type: str,
        page: int,
        limit: int,
    ) -> list[RecommendationItem]:
        """获取分页的内容推荐（兼容游标分页调用）

        Args:
            user_id: 用户ID
            content_type: 内容类型
            page: 页码（从1开始）
            limit: 每页大小

        Returns:
            推荐项列表
        """
        try:
            # 调用现有的推荐方法
            result = await self.get_recommendations(
                db=None,  # 这个方法不需要db
                user_id=user_id,
                content_type=content_type,
                page=page,
                page_size=limit,
            )
            return result.items
        except Exception:
            return []

    async def get_similar_content(
        self,
        content_type: str,
        content_id: int,
        limit: int,
    ) -> list[RecommendationItem]:
        """获取相似内容

        Args:
            content_type: 内容类型
            content_id: 内容ID
            limit: 返回数量限制

        Returns:
            相似内容推荐项列表
        """
        try:
            # 从相似内容池获取数据
            rec_pool_key = f"rec_pool:similar:{content_type}:{content_id}"
            recs = await sorted_set_get_range(
                rec_pool_key, 0, limit - 1, with_scores=True, desc=True
            )

            items = []
            for item, score in recs:
                try:
                    items.append(
                        RecommendationItem(
                            content_type=content_type,
                            content_id=int(item),
                            score=float(score),
                            reason="similar_content",
                        )
                    )
                except (ValueError, TypeError):
                    continue

            return items
        except Exception:
            return []
